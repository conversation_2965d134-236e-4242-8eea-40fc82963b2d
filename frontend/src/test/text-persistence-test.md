# 文字元素持久化测试指南

## 测试目标
确保文字元素的所有属性修改（无论是通过设置面板还是canvas控制框）在页面刷新后都能完全恢复。

## 测试步骤

### 1. 基础文字属性测试
1. 添加一个文字元素
2. 在设置面板中修改以下属性：
   - 字体大小 (fontSize)
   - 字体家族 (fontFamily)
   - 字体颜色 (fontColor)
   - 文字对齐 (textAlign)
   - 行高 (lineHeight)
   - 字符间距 (charSpacing)
3. 刷新页面
4. 验证所有属性是否正确恢复

### 2. 文字样式测试
1. 在设置面板中修改文字样式：
   - 粗体 (bold)
   - 斜体 (italic)
   - 下划线 (underlined)
   - 删除线 (strikethrough)
2. 刷新页面
3. 验证样式是否正确恢复

### 3. 高级属性测试
1. 在高级设置中修改：
   - 描边宽度 (strokeWidth)
   - 描边颜色 (strokeColor)
   - 阴影颜色 (shadowColor)
   - 阴影模糊 (shadowBlur)
   - 阴影偏移X (shadowOffsetX)
   - 阴影偏移Y (shadowOffsetY)
   - 背景颜色 (backgroundColor)
2. 刷新页面
3. 验证所有高级属性是否正确恢复

### 4. Canvas控制框测试
1. 通过canvas控制框修改：
   - 文字位置 (拖拽移动)
   - 文字大小 (拖拽调整)
   - 文字旋转 (旋转控制点)
   - 文字内容 (双击编辑)
2. 刷新页面
3. 验证所有通过canvas修改的属性是否正确恢复

### 5. 混合修改测试
1. 同时使用设置面板和canvas控制框修改文字属性
2. 刷新页面
3. 验证所有修改是否都能正确恢复

## 验证要点

### 数据持久化
- 检查localStorage中是否正确保存了文字元素的所有属性
- 确认属性值的类型和格式正确

### UI同步
- 设置面板中的控件值应与元素属性保持同步
- Canvas中的文字显示应与保存的属性一致

### 默认值处理
- 新创建的文字元素应包含所有必要的默认属性
- 缺失的属性应使用合理的默认值

## 预期结果
所有文字属性修改在页面刷新后都应该完全恢复，包括：
- 视觉效果与修改前完全一致
- 设置面板中的控件状态正确
- Canvas中的文字元素可以正常交互

## 故障排除
如果发现属性未正确恢复，检查：
1. 属性是否在element.properties中正确保存
2. refreshElement方法是否正确应用了所有属性
3. 设置面板的useEffect是否正确同步了状态
4. object:modified事件是否正确触发了保存
